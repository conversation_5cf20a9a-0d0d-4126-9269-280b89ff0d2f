package com.uit.tms.views.components.calendar;

import com.uit.tms.entity.TaskEntity;
import com.uit.tms.enums.InterFontStyleEnum;
import com.uit.tms.enums.TaskStatusEnum;
import com.uit.tms.utils.FontUtils;

import javax.swing.*;
import javax.swing.border.EmptyBorder;
import javax.swing.border.LineBorder;
import java.awt.*;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Date;

public class CalendarGrid extends JPanel {
    private int year;
    private int month;
    private List<TaskEntity> tasks;
    private final JPanel daysPanel;

    // Thêm biến để lưu trữ custom date range
    private Date customRangeStart;
    private Date customRangeEnd;

    // Thêm biến để theo dõi ngày đầu và cuối của range
    private boolean isRangeStartDate = false;
    private boolean isRangeEndDate = false;

    // Add to class fields
    private boolean highlightOverdueTasks = false; // Default to false

    public CalendarGrid() {
        this.tasks = new ArrayList<>();
        
        setLayout(new BorderLayout());
        setBackground(Color.WHITE);
        
        // Create header with day names
        JPanel headerPanel = new JPanel(new GridLayout(1, 7));
        headerPanel.setBackground(new Color(245, 245, 245));

        String[] dayNames = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
        for (String dayName : dayNames) {
            JLabel dayLabel = new JLabel(dayName, SwingConstants.CENTER);
            dayLabel.setFont(FontUtils.interFont(InterFontStyleEnum.SemiBold, 14));
            dayLabel.setForeground(new Color(100, 100, 100));
            dayLabel.setBorder(new EmptyBorder(10, 0, 10, 0));
            headerPanel.add(dayLabel);
        }
        
        // Create panel for calendar days
        daysPanel = new JPanel(new GridLayout(0, 7));
        daysPanel.setBackground(Color.WHITE);
        
        add(headerPanel, BorderLayout.NORTH);
        add(daysPanel, BorderLayout.CENTER);
    }
    
    public void setYearMonth(int year, int month) {
        this.year = year;
        this.month = month;
    }
    
    public void setTasks(List<TaskEntity> tasks) {
        this.tasks = tasks;
    }
    
    public void setCustomDateRange(Date start, Date end) {
        // Ensure start is before end
        if (start == null || end == null) {
            this.customRangeStart = null;
            this.customRangeEnd = null;
        } else {
            Calendar startCal = Calendar.getInstance();
            startCal.setTime(start);
            startCal.set(Calendar.HOUR_OF_DAY, 0);
            startCal.set(Calendar.MINUTE, 0);
            startCal.set(Calendar.SECOND, 0);
            startCal.set(Calendar.MILLISECOND, 0);
            
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(end);
            endCal.set(Calendar.HOUR_OF_DAY, 23);
            endCal.set(Calendar.MINUTE, 59);
            endCal.set(Calendar.SECOND, 59);
            endCal.set(Calendar.MILLISECOND, 999);
            
            this.customRangeStart = startCal.getTime();
            this.customRangeEnd = endCal.getTime();
        }
        
        refresh(); // Refresh để hiển thị highlight
    }
    
    public void refresh() {
        daysPanel.removeAll();
        
        Calendar calendar = Calendar.getInstance();
        calendar.set(year, month - 1, 1); // Month is 0-based in Calendar
        
        int firstDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1; // Adjust to 0-based
        int daysInMonth = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        
        // Add empty cells for days before the first day of the month
        for (int i = 0; i < firstDayOfWeek; i++) {
            JPanel emptyPanel = createEmptyDayPanel();
            daysPanel.add(emptyPanel);
        }
        
        // Add cells for each day of the month
        for (int day = 1; day <= daysInMonth; day++) {
            JPanel dayPanel = createDayPanel(day);
            daysPanel.add(dayPanel);
        }
        
        // Add empty cells to complete the grid if needed
        int totalCells = daysPanel.getComponentCount();
        int rowsNeeded = (int) Math.ceil(totalCells / 7.0);
        int totalNeededCells = rowsNeeded * 7;
        
        for (int i = totalCells; i < totalNeededCells; i++) {
            JPanel emptyPanel = createEmptyDayPanel();
            daysPanel.add(emptyPanel);
        }
        
        revalidate();
        repaint();
    }
    
    private JPanel createEmptyDayPanel() {
        JPanel panel = new JPanel();
        panel.setBackground(new Color(250, 250, 250));
        panel.setBorder(new LineBorder(new Color(240, 240, 240), 1));
        return panel;
    }
    
    private JPanel createDayPanel(int day) {
        JPanel panel = new JPanel();
        panel.setLayout(new BorderLayout());
        
        // Reset range flags
        isRangeStartDate = false;
        isRangeEndDate = false;
        
        // Check if this day is the start or end of the custom range
        boolean isInCustomRange = isDateInCustomRange(year, month - 1, day);
        
        // Set background color based on whether it's in custom range
        if (isInCustomRange) {
            // Use a different color for the start and end dates
            if (isRangeStartDate || isRangeEndDate) {
                panel.setBackground(new Color(210, 230, 255));
            } else {
                panel.setBackground(new Color(230, 241, 255));
            }
        } else {
            panel.setBackground(Color.WHITE);
        }
        
        panel.setBorder(new LineBorder(new Color(240, 240, 240), 1));
        
        // Add double-click listener to create a new task
        panel.addMouseListener(new MouseAdapter() {
            @Override
            public void mouseClicked(MouseEvent e) {
                if (e.getClickCount() == 2) {
                    createNewTask(day);
                }
            }
        });

        // Day number label with better styling
        JPanel dayHeaderPanel = new JPanel(new BorderLayout());
        dayHeaderPanel.setOpaque(false);
        dayHeaderPanel.setBorder(new EmptyBorder(5, 5, 5, 5));
        
        JLabel dayLabel = new JLabel(String.valueOf(day));
        dayLabel.setFont(FontUtils.interFont(InterFontStyleEnum.SemiBold, 14));
        dayLabel.setBorder(new EmptyBorder(2, 5, 2, 5));
        
        // Check if today
        Calendar today = Calendar.getInstance();
        Calendar thisDay = Calendar.getInstance();
        thisDay.set(year, month - 1, day);
        
        if (today.get(Calendar.YEAR) == year && 
            today.get(Calendar.MONTH) == month - 1 && 
            today.get(Calendar.DAY_OF_MONTH) == day) {
            
            // Create a circular background for today's date
            JPanel todayIndicator = new JPanel() {
                @Override
                protected void paintComponent(Graphics g) {
                    super.paintComponent(g);
                    Graphics2D g2d = (Graphics2D) g.create();
                    g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g2d.setColor(new Color(66, 133, 244));
                    int size = Math.min(getWidth(), getHeight()) - 4;
                    g2d.fillOval((getWidth() - size) / 2, (getHeight() - size) / 2, size, size);
                    g2d.dispose();
                }
            };
            todayIndicator.setOpaque(false);
            todayIndicator.setPreferredSize(new Dimension(28, 28));
            
            dayLabel.setForeground(Color.WHITE);
            dayLabel.setHorizontalAlignment(SwingConstants.CENTER);
            
            todayIndicator.setLayout(new BorderLayout());
            todayIndicator.add(dayLabel, BorderLayout.CENTER);
            
            dayHeaderPanel.add(todayIndicator, BorderLayout.WEST);
        } else {
            // If in custom range but not today, use a different color for the day number
            if (isInCustomRange) {
                dayLabel.setForeground(new Color(0, 120, 212)); // Blue text for days in custom range
            }
            dayHeaderPanel.add(dayLabel, BorderLayout.WEST);
        }
        
        panel.add(dayHeaderPanel, BorderLayout.NORTH);
        
        // Add tasks for this day
        List<TaskEntity> tasksForDay = getTasksForDay(day);
        if (!tasksForDay.isEmpty()) {
            JPanel tasksPanel = new JPanel();
            tasksPanel.setLayout(new BoxLayout(tasksPanel, BoxLayout.Y_AXIS));
            tasksPanel.setOpaque(false); // Make transparent to show custom range background
            
            // Limit to showing max 3 tasks with "more" indicator
            int maxTasksToShow = Math.min(tasksForDay.size(), 3);
            for (int i = 0; i < maxTasksToShow; i++) {
                TaskEntity task = tasksForDay.get(i);
                JPanel taskPanel = createTaskPanel(task);
                tasksPanel.add(taskPanel);
            }
            
            // Add "more" indicator if needed
            if (tasksForDay.size() > 3) {
                JLabel moreLabel = new JLabel("+" + (tasksForDay.size() - 3) + " more");
                moreLabel.setFont(FontUtils.interFont(InterFontStyleEnum.Medium, 11));
                moreLabel.setForeground(new Color(100, 100, 100));
                moreLabel.setBorder(new EmptyBorder(2, 5, 2, 5));
                
                JPanel morePanel = new JPanel(new BorderLayout());
                morePanel.setOpaque(false); // Make transparent to show custom range background
                morePanel.add(moreLabel, BorderLayout.WEST);
                
                tasksPanel.add(morePanel);
            }
            
            JScrollPane scrollPane = new JScrollPane(tasksPanel);
            scrollPane.setBorder(null);
            scrollPane.setHorizontalScrollBarPolicy(JScrollPane.HORIZONTAL_SCROLLBAR_NEVER);
            scrollPane.setVerticalScrollBarPolicy(JScrollPane.VERTICAL_SCROLLBAR_AS_NEEDED);
            scrollPane.getViewport().setOpaque(false); // Make viewport transparent
            scrollPane.setOpaque(false); // Make scrollpane transparent
            
            panel.add(scrollPane, BorderLayout.CENTER);
        }
        
        return panel;
    }
    
    private JPanel createTaskPanel(TaskEntity task) {
        // Check if task is overdue and should be highlighted
        boolean isOverdue = highlightOverdueTasks && isTaskOverdue(task);
        
        // Create task cell with overdue information
        TaskCell taskCell = new TaskCell(task, isOverdue);
        
        // Wrap TaskCell in a panel with margin
        JPanel wrapperPanel = new JPanel(new BorderLayout());
        wrapperPanel.setBackground(Color.WHITE);
        wrapperPanel.setBorder(new EmptyBorder(2, 0, 2, 0)); // Add vertical spacing
        wrapperPanel.add(taskCell, BorderLayout.CENTER);
        
        return wrapperPanel;
    }
    
    private List<TaskEntity> getTasksForDay(int day) {
        List<TaskEntity> tasksForDay = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        
        for (TaskEntity task : tasks) {
            if (task.getDueDate() != null) {
                cal.setTime(task.getDueDate());
                if (cal.get(Calendar.DAY_OF_MONTH) == day) {
                    tasksForDay.add(task);
                }
            }
        }
        
        return tasksForDay;
    }

    // Update isDateInCustomRange to set isRangeStartDate and isRangeEndDate
    private boolean isDateInCustomRange(int year, int month, int day) {
        if (customRangeStart == null || customRangeEnd == null) {
            return false;
        }
        
        // Create a calendar for the given date
        Calendar cal = Calendar.getInstance();
        cal.set(year, month, day, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date date = cal.getTime();
        
        // Create calendars for the start and end of the custom range
        Calendar startCal = Calendar.getInstance();
        startCal.setTime(customRangeStart);
        startCal.set(Calendar.HOUR_OF_DAY, 0);
        startCal.set(Calendar.MINUTE, 0);
        startCal.set(Calendar.SECOND, 0);
        startCal.set(Calendar.MILLISECOND, 0);
        
        Calendar endCal = Calendar.getInstance();
        endCal.setTime(customRangeEnd);
        endCal.set(Calendar.HOUR_OF_DAY, 23);
        endCal.set(Calendar.MINUTE, 59);
        endCal.set(Calendar.SECOND, 59);
        endCal.set(Calendar.MILLISECOND, 999);

        boolean inRange = (date.compareTo(startCal.getTime()) >= 0) && 
                          (date.compareTo(endCal.getTime()) <= 0);
        
        if (inRange) {
            boolean isStartDate = cal.get(Calendar.YEAR) == startCal.get(Calendar.YEAR) &&
                                 cal.get(Calendar.MONTH) == startCal.get(Calendar.MONTH) &&
                                 cal.get(Calendar.DAY_OF_MONTH) == startCal.get(Calendar.DAY_OF_MONTH);
            
            boolean isEndDate = cal.get(Calendar.YEAR) == endCal.get(Calendar.YEAR) &&
                               cal.get(Calendar.MONTH) == endCal.get(Calendar.MONTH) &&
                               cal.get(Calendar.DAY_OF_MONTH) == endCal.get(Calendar.DAY_OF_MONTH);
            
            isRangeStartDate = isStartDate;
            isRangeEndDate = isEndDate;
        } else {
            isRangeStartDate = false;
            isRangeEndDate = false;
        }
        
        return inRange;
    }

    /**
     * Set whether to highlight overdue tasks
     */
    public void setHighlightOverdueTasks(boolean highlight) {
        this.highlightOverdueTasks = highlight;
    }

    /**
     * Check if a task is overdue
     */
    private boolean isTaskOverdue(TaskEntity task) {
        // Skip completed tasks
        if (task.getStatus() == TaskStatusEnum.DONE) {
            return false;
        }
        
        // Check if due date is in the past
        if (task.getDueDate() != null) {
            Calendar today = Calendar.getInstance();
            today.set(Calendar.HOUR_OF_DAY, 0);
            today.set(Calendar.MINUTE, 0);
            today.set(Calendar.SECOND, 0);
            today.set(Calendar.MILLISECOND, 0);
            
            Calendar dueDate = Calendar.getInstance();
            dueDate.setTime(task.getDueDate());
            dueDate.set(Calendar.HOUR_OF_DAY, 0);
            dueDate.set(Calendar.MINUTE, 0);
            dueDate.set(Calendar.SECOND, 0);
            dueDate.set(Calendar.MILLISECOND, 0);
            
            return dueDate.before(today);
        }
        
        return false;
    }

    /**
     * Creates a new task when a calendar cell is double-clicked
     * @param day The day of the month that was clicked
     */
    private void createNewTask(int day) {
        // Create a date for the selected day
        Calendar cal = Calendar.getInstance();
        cal.set(year, month - 1, day);
        Date selectedDate = cal.getTime();

        // Create a new task with the selected date as due date
        TaskEntity newTask = new TaskEntity();
        newTask.setDueDate(selectedDate);

        // Show task creation dialog
        Frame parentFrame = (Frame) SwingUtilities.getWindowAncestor(this);
        if (parentFrame != null) {
            TaskDetailDialog dialog = new TaskDetailDialog((JFrame) parentFrame, newTask, true);
            dialog.setVisible(true);

            // Refresh calendar after dialog is closed to show the new task if it was created
            refresh();
        }
    }
}
